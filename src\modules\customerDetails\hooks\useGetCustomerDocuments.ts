import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'

export default function useGetCustomerDocuments(customerId?: string) {
  const initialFilters = {
    page: 1,
    limit: 10,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters)
  const {data: response, isFetching} = Api.useGetQuery(
    customerId ? `/customers/${customerId}/documents` : '',
    {
      queryId: customerId ? `customer-documents-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId,
    }
  )
  return {
    documents: response?.data || [],
    isLoading: isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
