import useApi from '../../../services/useApi'
import useFilters from '../../../hook/useFilters'

interface RtvDetailsParams {
  parent_sku?: string
}

const useGetParentRtvDetails = (params: RtvDetailsParams = {}) => {
  const Api = useApi()

  const initialFilters = {
    parent_sku: params.parent_sku || '',
    returns_type: 'rtv',
  }

  const {filters, setSingleFilter} = useFilters(initialFilters)

  const {
    data: response,
    isFetching,
    refetch,
  } = Api.useGetQuery(
    `/analytics/replenishment/zoho/returns/parent/data`,
    {
      queryId: `rtv-details-parent-${params.parent_sku}`,
      filters: filters,
      isToast: false,
    },
    {
      enabled: !!params.parent_sku,
    }
  )

  const fetchParentRtvDetails = (parentSku: string) => {
    setSingleFilter('parent_sku', parentSku)
    refetch()
  }

  return {
    parentRtvDetails: response?.data,
    isLoading: isFetching,
    filters,
    fetchParentRtvDetails,
    refetch,
  }
}

export default useGetParentRtvDetails
