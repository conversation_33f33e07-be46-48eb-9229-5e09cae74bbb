import {createContext} from 'react'

export const CustomerDetailsContext = createContext({
  overview: {},
  accountInfo: {},
  addresses: [],
  orders: [],
  financialPayment: {},
  rewards: [],
  pricing: [],
  systemActivity: [],
  documents: [],
  isLoading: false,
  // CRUD operations and handlers (placeholders)
  createCustomer: async () => {},
  updateCustomer: async () => {},
  deleteCustomer: async () => {},
  onSearch: () => {},
  onPageChange: () => {},
  onSortingChange: () => {},
})
