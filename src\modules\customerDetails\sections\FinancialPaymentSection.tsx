import React from 'react'
import {KTSVG} from '../../../_metronic/helpers'

const InfoField = ({
  icon,
  label,
  value,
  className = '',
}: {
  icon?: string
  label: string
  value: string
  className?: string
}) => (
  <div className={`d-flex align-items-center mb-3 ${className}`}>
    {icon && (
      <div className='me-3'>
        <KTSVG path={icon} className='svg-icon-2 text-muted' />
      </div>
    )}
    <div className='flex-grow-1'>
      <div className='text-muted fs-7 fw-semibold'>{label}:</div>
      <div className='fs-6 fw-bold text-gray-800'>{value}</div>
    </div>
  </div>
)

const FinancialPaymentSection = () => (
  <div className='row g-5 mb-5'>
    {/* Payment & Credit */}
    <div className='col-12'>
      <div className='card card-flush border'>
        <div className='card-header border-0 pt-6'>
          <div className='card-title'>
            <div className='d-flex align-items-center'>
              <KTSVG
                path='/media/icons/duotune/finance/fin010.svg'
                className='svg-icon-2 text-primary me-2'
              />
              <h3 className='fw-bold m-0'>Payment & Credit</h3>
            </div>
          </div>
        </div>
        <div className='card-body pt-0'>
          <div className='row'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/finance/fin006.svg'
                label='Payment Term'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/finance/fin010.svg'
                label='Payment Term Notes'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Tax Exempt'
                value='No'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/finance/fin010.svg'
                label='Credit Limit'
                value='$50,000'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Payment Term Approved By'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/finance/fin006.svg'
                label='Currency'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/finance/fin006.svg'
                label='Unpaid Balance'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Payment Term Approved Date & Time'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Payment Term Risk'
                value='Not Set'
              />
            </div>
          </div>
          <div className='row mt-4'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/finance/fin006.svg'
                label='Outstanding Balance'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Payment Term Issues'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Cash & Carry */}
    <div className='col-12'>
      <div className='card card-flush border'>
        <div className='card-header border-0 pt-6'>
          <div className='card-title'>
            <div className='d-flex align-items-center'>
              <KTSVG
                path='/media/icons/duotune/finance/fin006.svg'
                className='svg-icon-2 text-warning me-2'
              />
              <h3 className='fw-bold m-0'>Cash & Carry</h3>
            </div>
          </div>
        </div>
        <div className='card-body pt-0'>
          <div className='row'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Cash & Carry Check Approved?'
                value='No'
              />
              <InfoField
                icon='/media/icons/duotune/finance/fin006.svg'
                label='Check Approved Amount Limit'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='Check Approved By'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Noted for Check/CNC'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Check Approved Date & Time'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Does The Customer have account w/CTOS?'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Credit Card */}
    <div className='col-12'>
      <div className='card card-flush border'>
        <div className='card-header border-0 pt-6'>
          <div className='card-title'>
            <div className='d-flex align-items-center'>
              <KTSVG
                path='/media/icons/duotune/finance/fin002.svg'
                className='svg-icon-2 text-success me-2'
              />
              <h3 className='fw-bold m-0'>Credit Card</h3>
            </div>
          </div>
        </div>
        <div className='card-body pt-0'>
          <div className='row'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Customer Credit Card Fees Exempt'
                value='No'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='Credit Card Fees Exempt Approved By'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Credit Card Fees Exempt Approved Date'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* ACH */}
    <div className='col-12'>
      <div className='card card-flush border'>
        <div className='card-header border-0 pt-6'>
          <div className='card-title'>
            <div className='d-flex align-items-center'>
              <KTSVG
                path='/media/icons/duotune/finance/fin001.svg'
                className='svg-icon-2 text-info me-2'
              />
              <h3 className='fw-bold m-0'>ACH</h3>
            </div>
          </div>
        </div>
        <div className='card-body pt-0'>
          <div className='row'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='ACH/E-Check Approved'
                value='No'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='ACH Form Received & Uploaded'
                value='No'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='ACH/E-Check Approved By'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='ACH Form Uploaded By'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='ACH/E-Checked Approved Date & Time'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='ACH Form Received Uploaded Date'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export default FinancialPaymentSection
