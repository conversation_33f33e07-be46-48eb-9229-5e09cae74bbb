import React from 'react'
import {KTSVG} from '../../../_metronic/helpers'

const OverviewSection = () => (
  <div className='row g-5 mb-5'>
    {/* Top summary cards */}
    <div className='col-md-3'>
      <div className='card border card-flush h-100'>
        <div className='card-body p-7 text-center'>
          <div className='mb-4 d-flex justify-content-center'>
            <div className='symbol symbol-50px bg-light-primary d-flex align-items-center justify-content-center'>
              <KTSVG path='/media/ad-theme-icons/blue-order-icon.svg' className='icon-24' />
            </div>
          </div>
          <div className='fw-bold fs-2 mb-1'>45</div>
          <div className='text-muted fs-6'>Total Orders</div>
        </div>
      </div>
    </div>
    <div className='col-md-3'>
      <div className='card border card-flush h-100'>
        <div className='card-body p-7 text-center'>
          <div className='mb-4 d-flex justify-content-center'>
            <div className='symbol symbol-50px bg-light-primary d-flex align-items-center justify-content-center'>
              <KTSVG path='/media/ad-theme-icons/blue-doller-icon.svg' className='icon-24' />
            </div>
          </div>
          <div className='fw-bold fs-2 mb-1'>$125,000</div>
          <div className='text-muted fs-6'>Total Spent</div>
        </div>
      </div>
    </div>
    <div className='col-md-3'>
      <div className='card border card-flush h-100'>
        <div className='card-body p-7 text-center'>
          <div className='mb-4 d-flex justify-content-center'>
            <div className='symbol symbol-50px bg-light-primary d-flex align-items-center justify-content-center'>
              <KTSVG path='/media/ad-theme-icons/blue-pause-icon.svg' className='icon-24' />
            </div>
          </div>
          <div className='fw-bold fs-2 mb-1'>$2,777</div>
          <div className='text-muted fs-6'>Avg Order Value</div>
        </div>
      </div>
    </div>
    <div className='col-md-3'>
      <div className='card border card-flush h-100'>
        <div className='card-body p-7 text-center'>
          <div className='mb-4 d-flex justify-content-center'>
            <div className='symbol symbol-50px bg-light-primary d-flex align-items-center justify-content-center'>
              <KTSVG path='/media/ad-theme-icons/blue-star-icon.svg' className='icon-24' />
            </div>
          </div>
          <div className='fw-bold fs-2 mb-1'>$150,000</div>
          <div className='text-muted fs-6'>Lifetime Value</div>
        </div>
      </div>
    </div>
    {/* Details cards */}
    <div className='col-md-4'>
      <div className='card border card-flush h-100 mt-5'>
        <div className='card-body p-7'>
          <div className='fw-bold mb-2'>Status & Dates</div>
          <div>
            Status: <span className='fw-semibold text-success'>Active</span>
          </div>
          <div>Created: 15/01/2023</div>
          <div>Last Modified: -</div>
          <div>Last Activity: -</div>
        </div>
      </div>
    </div>
    <div className='col-md-4'>
      <div className='card border card-flush h-100 mt-5'>
        <div className='card-body p-7'>
          <div className='fw-bold mb-2'>Contact</div>
          <div>Email: <EMAIL></div>
          <div>Phone: +****************</div>
          <div>Website: https://acme.com</div>
          <div>Owner: -</div>
        </div>
      </div>
    </div>
    <div className='col-md-4'>
      <div className='card border card-flush h-100 mt-5'>
        <div className='card-body p-7'>
          <div className='fw-bold mb-2'>Financial</div>
          <div>Annual Revenue: $10,000,000</div>
          <div>Credit Limit: $50,000</div>
          <div>Credit Used: $15,000</div>
          <div>Outstanding Balance: -</div>
        </div>
      </div>
    </div>
  </div>
)

export default OverviewSection
