import React from 'react'
import {KTSVG} from '../../../_metronic/helpers'

const OverviewSection = () => (
  <div className='row g-5 mb-5'>
    {/* Top summary cards */}
    <div className='col-md-3'>
      <div className='card bg-light-info border-0 h-100'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center'>
            <div className='flex-grow-1'>
              <div className='text-muted fs-7 fw-semibold mb-1'>Total Orders</div>
              <div className='fw-bold fs-1 text-gray-800'>45</div>
            </div>
            <div className='symbol symbol-50px bg-info bg-opacity-10'>
              <div className='symbol-label'>
                <KTSVG
                  path='/media/icons/duotune/ecommerce/ecm002.svg'
                  className='svg-icon-2 text-info'
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className='col-md-3'>
      <div className='card bg-light-primary border-0 h-100'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center'>
            <div className='flex-grow-1'>
              <div className='text-muted fs-7 fw-semibold mb-1'>Total Spent</div>
              <div className='fw-bold fs-1 text-gray-800'>$125,000</div>
            </div>
            <div className='symbol symbol-50px bg-primary bg-opacity-10'>
              <div className='symbol-label'>
                <KTSVG
                  path='/media/icons/duotune/finance/fin010.svg'
                  className='svg-icon-2 text-primary'
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className='col-md-3'>
      <div className='card bg-light-warning border-0 h-100'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center'>
            <div className='flex-grow-1'>
              <div className='text-muted fs-7 fw-semibold mb-1'>Avg Order Value</div>
              <div className='fw-bold fs-1 text-gray-800'>$2,777</div>
            </div>
            <div className='symbol symbol-50px bg-warning bg-opacity-10'>
              <div className='symbol-label'>
                <KTSVG
                  path='/media/icons/duotune/general/gen025.svg'
                  className='svg-icon-2 text-warning'
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className='col-md-3'>
      <div className='card bg-light-success border-0 h-100'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center'>
            <div className='flex-grow-1'>
              <div className='text-muted fs-7 fw-semibold mb-1'>Lifetime Value</div>
              <div className='fw-bold fs-1 text-gray-800'>$150,000</div>
            </div>
            <div className='symbol symbol-50px bg-success bg-opacity-10'>
              <div className='symbol-label'>
                <KTSVG
                  path='/media/icons/duotune/general/gen024.svg'
                  className='svg-icon-2 text-success'
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {/* Details cards */}
    <div className='col-md-4'>
      <div className='card border-0 bg-light h-100 mt-5'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-4'>
            <KTSVG
              path='/media/icons/duotune/general/gen014.svg'
              className='svg-icon-2 text-primary me-2'
            />
            <h5 className='fw-bold m-0 text-gray-800'>Status & Dates</h5>
          </div>
          <div className='mb-3'>
            <div className='d-flex align-items-center mb-2'>
              <KTSVG
                path='/media/icons/duotune/general/gen043.svg'
                className='svg-icon-5 text-muted me-2'
              />
              <span className='text-muted fs-7'>Status:</span>
              <span className='badge badge-success ms-2'>Active</span>
            </div>
          </div>
          <div className='mb-3'>
            <div className='d-flex align-items-center mb-2'>
              <KTSVG
                path='/media/icons/duotune/general/gen014.svg'
                className='svg-icon-5 text-muted me-2'
              />
              <span className='text-muted fs-7'>Created:</span>
              <span className='text-gray-800 fw-semibold ms-2'>15/01/2023</span>
            </div>
          </div>
          <div className='mb-3'>
            <div className='d-flex align-items-center mb-2'>
              <KTSVG
                path='/media/icons/duotune/general/gen014.svg'
                className='svg-icon-5 text-muted me-2'
              />
              <span className='text-muted fs-7'>Last Modified:</span>
              <span className='text-gray-800 fw-semibold ms-2'>-</span>
            </div>
          </div>
          <div className='mb-3'>
            <div className='d-flex align-items-center mb-2'>
              <KTSVG
                path='/media/icons/duotune/general/gen007.svg'
                className='svg-icon-5 text-muted me-2'
              />
              <span className='text-muted fs-7'>Last Activity:</span>
              <span className='text-gray-800 fw-semibold ms-2'>-</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className='col-md-4'>
      <div className='card border-0 bg-light h-100 mt-5'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-4'>
            <KTSVG
              path='/media/icons/duotune/communication/com014.svg'
              className='svg-icon-2 text-primary me-2'
            />
            <h5 className='fw-bold m-0 text-gray-800'>Contact</h5>
          </div>
          <div className='mb-3'>
            <div className='d-flex align-items-center mb-2'>
              <KTSVG
                path='/media/icons/duotune/communication/com011.svg'
                className='svg-icon-5 text-muted me-2'
              />
              <span className='text-muted fs-7'>Email:</span>
              <span className='text-gray-800 fw-semibold ms-2'><EMAIL></span>
            </div>
          </div>
          <div className='mb-3'>
            <div className='d-flex align-items-center mb-2'>
              <KTSVG
                path='/media/icons/duotune/communication/com005.svg'
                className='svg-icon-5 text-muted me-2'
              />
              <span className='text-muted fs-7'>Phone:</span>
              <span className='text-gray-800 fw-semibold ms-2'>+****************</span>
            </div>
          </div>
          <div className='mb-3'>
            <div className='d-flex align-items-center mb-2'>
              <KTSVG
                path='/media/icons/duotune/communication/com013.svg'
                className='svg-icon-5 text-muted me-2'
              />
              <span className='text-muted fs-7'>Website:</span>
              <span className='text-gray-800 fw-semibold ms-2'>https://acme.com</span>
            </div>
          </div>
          <div className='mb-3'>
            <div className='d-flex align-items-center mb-2'>
              <KTSVG
                path='/media/icons/duotune/communication/com006.svg'
                className='svg-icon-5 text-muted me-2'
              />
              <span className='text-muted fs-7'>Owner:</span>
              <span className='text-gray-800 fw-semibold ms-2'>-</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className='col-md-4'>
      <div className='card border-0 bg-light h-100 mt-5'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-4'>
            <KTSVG
              path='/media/icons/duotune/finance/fin010.svg'
              className='svg-icon-2 text-primary me-2'
            />
            <h5 className='fw-bold m-0 text-gray-800'>Financial</h5>
          </div>
          <div className='mb-3'>
            <div className='d-flex align-items-center mb-2'>
              <KTSVG
                path='/media/icons/duotune/finance/fin006.svg'
                className='svg-icon-5 text-muted me-2'
              />
              <span className='text-muted fs-7'>Annual Revenue:</span>
              <span className='text-gray-800 fw-semibold ms-2'>$10,000,000</span>
            </div>
          </div>
          <div className='mb-3'>
            <div className='d-flex align-items-center mb-2'>
              <KTSVG
                path='/media/icons/duotune/finance/fin010.svg'
                className='svg-icon-5 text-muted me-2'
              />
              <span className='text-muted fs-7'>Credit Limit:</span>
              <span className='text-gray-800 fw-semibold ms-2'>$50,000</span>
            </div>
          </div>
          <div className='mb-3'>
            <div className='d-flex align-items-center mb-2'>
              <KTSVG
                path='/media/icons/duotune/finance/fin006.svg'
                className='svg-icon-5 text-muted me-2'
              />
              <span className='text-muted fs-7'>Credit Used:</span>
              <span className='text-gray-800 fw-semibold ms-2'>$15,000</span>
            </div>
          </div>
          <div className='mb-3'>
            <div className='d-flex align-items-center mb-2'>
              <KTSVG
                path='/media/icons/duotune/finance/fin006.svg'
                className='svg-icon-5 text-muted me-2'
              />
              <span className='text-muted fs-7'>Outstanding Balance:</span>
              <span className='text-gray-800 fw-semibold ms-2'>-</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export default OverviewSection
