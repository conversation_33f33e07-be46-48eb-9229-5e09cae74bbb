import {formatDate} from '../../../utils/date'
import {capitalizeFirstLetter} from '../../../utils/string'
import {HasValueOrNot, convertToPoints, formatPrice} from '../../../utils/common'
import {parseNumber} from '../../../utils/parseNumber'

export const STATUS_OPTIONS = [
  {
    value: '',
    label: 'Select Status',
  },
  {
    value: 'active',
    label: 'Active',
  },
  {
    value: 'inactive',
    label: 'Inactive',
  },
]

type Rewards = {
  id: string
  coupon_value: string
  reward_points: number
  created_at: number
  updated_at: number
  created_by: any
  updated_by: any
  status: string
  min_purchase: string
}

export function dataToRewards(data: Rewards) {
  return {
    id: data['id'],
    coupon_value: data['coupon_value'],
    reward_points: convertToPoints(data['reward_points']),
    min_order_amount: data['min_purchase'] || '-',
    created_at: data?.['created_at'] ? formatDate(data['created_at'], false) : '-',
    updated_at: data?.['updated_at'] ? formatDate(data['updated_at'], false) : '-',
    created_by: data?.['created_by']?.user_name ? data?.['created_by']?.user_name : '',
    updated_by: data?.['updated_by']?.user_name ? data?.['updated_by']?.user_name : '',
    status: data?.['status'] ? capitalizeFirstLetter(data['status']) : '-',
  }
}

type Customers = {
  id: string
  name: string
  email: string
  store_id: string
  firstName: string
  lastName: string
  loyalty_points: string
  reward_count: string
  status: string
  reward_date: number
  customer_id: any
  customer_group_name: string
}

export default function parseCustomers(data: Customers) {
  return {
    id: HasValueOrNot(data?.['id']),
    customer_id: data?.['customer_id'],
    customerFirstNameLetter: data?.name
      ? data['name'].charAt(0).toUpperCase()
      : data?.firstName
      ? data.firstName.charAt(0).toUpperCase()
      : '',
    name: data?.name
      ? HasValueOrNot(data?.['name'])
      : `${HasValueOrNot(data?.firstName)} ${HasValueOrNot(data?.lastName)}`,
    email: HasValueOrNot(data?.['email']),
    store_id: HasValueOrNot(data['store_id']),
    loyalty_points: convertToPoints(data?.['loyalty_points']),
    reward_count: parseNumber(data?.['reward_count']) || 0,
    status: HasValueOrNot(data?.['status']),
    reward_date: data?.['reward_date'] ? formatDate(data?.['reward_date'], false) : '-',
    customerGroup: HasValueOrNot(data?.['customer_group_name']),
  }
}

export const couponDetailsTableColumns = [
  {
    key: 'amount',
    label: 'Coupon Value',
    headerStyle: 'min-w-120px',
  },
  {
    key: 'code',
    label: 'Coupon Code',
    headerStyle: 'min-w-175px',
  },
   {
    key: 'min_purchase',
    label: 'Min. Order amount',
    headerStyle: 'min-w-175px',
  },
  {
    key: 'expires',
    label: 'Expiry Date',
    headerStyle: 'min-w-125px',
  },
  {
    key: 'usage_date',
    label: 'Usage Date',
    headerStyle: 'min-w-200px',
  },
  {
    key: 'status',
    label: 'Status',
    headerStyle: 'min-w-125px',
  },
]

export const parseCouponDetails = (data: any) => {
  return data.map((item: any) => {
    return {
      couponValue: formatPrice(item?.amount, false) || '-',
      couponCode: item?.code || '-',
      expiryDate: item?.expires || '-',
      usageDate: item?.usage_date || null,
      status: item?.enabled ? 'Active' : 'Inactive',
      minPurchase: item?.min_purchase || '-',
    }
  })
}
