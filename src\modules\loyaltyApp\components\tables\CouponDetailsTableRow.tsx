import Date from '../../../../components/Date/Date'
import {getBadgeColor} from '../../../../utils/badge'

const CouponDetailsTableRow = ({row}: any) => {
  return (
    <tr>
      <td>{row.couponValue}</td>
      <td><span className='badge badge-lg badge-secondary'>{row.couponCode}</span></td>
      <td>{row.minPurchase}</td>
      <td>
        <Date date={row.expiryDate} />
      </td>
      <td>
        <Date date={row.usageDate} />
      </td>
      <td>
        <span className={`badge ${getBadgeColor(row.status, 'light')} badge-lg`}>
          <div className='align-items-center'>{row.status}</div>
        </span>
      </td>
    </tr>
  )
}
export default CouponDetailsTableRow
