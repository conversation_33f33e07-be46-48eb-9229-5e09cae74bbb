import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'

export default function useGetCustomerFinancialPayment(customerId?: string) {
  const initialFilters = {
    page: 1,
    limit: 1,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters)
  const {data: response, isFetching} = Api.useGetQuery(
    customerId ? `/customers/${customerId}/financial-payment` : '',
    {
      queryId: customerId ? `customer-financial-payment-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId,
    }
  )
  return {
    financialPayment: response?.data || {},
    isLoading: isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
