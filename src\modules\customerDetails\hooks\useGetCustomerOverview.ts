import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'

export default function useGetCustomerOverview(customerId?: string) {
  const initialFilters = {
    page: 1,
    limit: 1,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters)
  const {data: response, isFetching} = Api.useGetQuery(
    customerId ? `/customers/${customerId}/overview` : '',
    {
      queryId: customerId ? `customer-overview-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId,
    }
  )
  return {
    overview: response?.data || {},
    isLoading: isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
