export interface IOverview {
  // TODO: define fields
}
export interface IAccountInfo {
  // TODO: define fields
}
export interface IAddress {
  // TODO: define fields
}
export interface IOrder {
  // TODO: define fields
}
export interface IFinancialPayment {
  // TODO: define fields
}
export interface IReward {
  // TODO: define fields
}
export interface IPricing {
  // TODO: define fields
}
export interface ISystemActivity {
  // TODO: define fields
}
export interface IDocument {
  // TODO: define fields
}
