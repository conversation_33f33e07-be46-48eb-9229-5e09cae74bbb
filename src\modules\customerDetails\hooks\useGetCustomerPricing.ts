import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'

export default function useGetCustomerPricing(customerId?: string) {
  const initialFilters = {
    page: 1,
    limit: 10,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters)
  const {data: response, isFetching} = Api.useGetQuery(
    customerId ? `/customers/${customerId}/pricing` : '',
    {
      queryId: customerId ? `customer-pricing-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId,
    }
  )
  return {
    pricing: response?.data || [],
    isLoading: isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
