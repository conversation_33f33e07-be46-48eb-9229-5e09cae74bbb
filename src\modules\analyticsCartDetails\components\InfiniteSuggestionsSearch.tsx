/* eslint-disable react-hooks/exhaustive-deps */
import React, {useState, useEffect, useCallback} from 'react'
import {KTSVG} from '../../../_metronic/helpers'
import {InputText} from '../../../components/InputText'

interface InfiniteSuggestionsSearchProps {
  data: any[]
  onSearch: (term: string) => void
  onSelectOption: (item: any) => void
  onLoadMore: () => void
  register: any
  isLoading?: boolean
  disabled?: boolean
  error?: any
  inputClass?: string
  suggestionsClass?: string
  pageSize?: number
  value?: string
  setValue?: (term: string) => void
  onClear?: () => void
}

function InfiniteSuggestionsSearch({
  data,
  onSearch,
  onSelectOption,
  onLoadMore,
  register,
  isLoading = false,
  disabled = false,
  error,
  inputClass = '',
  suggestionsClass = '',
  pageSize = 10,
  value,
  setValue,
  onClear,
}: InfiniteSuggestionsSearchProps) {
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [hasMore, setHasMore] = useState(false)
  const [tempSearchTerm, setTempSearchTerm] = useState('')

  useEffect(() => {
    const timer = setTimeout(() => {
      if ((value ?? tempSearchTerm)) {
        onSearch(value ?? tempSearchTerm)
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [value, tempSearchTerm])

  useEffect(() => {
    setHasMore(data.length >= pageSize)
  }, [data, pageSize])

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue?.(e.target.value)
    setTempSearchTerm(e.target.value)
    setShowSuggestions(true)
  }

  const handleClear = () => {
    setValue?.('')
    setTempSearchTerm('')
    setShowSuggestions(false)
    onClear?.()
    onSearch?.('')
  }

  const handleSelectOption = (item: any) => {
    onSelectOption(item)
    setShowSuggestions(false)
  }

  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      if (isLoading || !hasMore) return

      const {scrollTop, clientHeight, scrollHeight} = e.currentTarget
      if (scrollTop + clientHeight >= scrollHeight - 5) {
        setHasMore(false)
        onLoadMore()
      }
    },
    [isLoading, hasMore, onLoadMore]
  )

  return (
    <div className='position-relative'>
      <div className='d-flex'>
        <KTSVG
          path='/media/icons/duotune/general/gen021.svg'
          className='svg-icon-1 position-absolute ms-6 mt-2'
        />
        <InputText
          id='sku'
          placeholder='Product SKU'
          inputClass={`form-control-solid ps-14 pe-10 ${inputClass} ${
            error ? 'border-danger' : ''
          }`}
          value={value ?? tempSearchTerm}
          register={register}
          error={error}
          disabled={disabled}
          onChange={handleSearchChange}
        />
        {(value ?? tempSearchTerm) && (
          <i
            className={`bi bi-x position-absolute end-0 cursor-pointer fs-2 mt-3 ${
              error ? 'me-10' : 'me-4'
            }`}
            onClick={handleClear}
          />
        )}
      </div>

      {showSuggestions &&
        ((value ?? tempSearchTerm)?.length > 0 || isLoading) &&
        !error &&
        data?.length !== 0 && (
          <div
            className={`list-group border rounded mt-1 ${suggestionsClass}`}
            style={{
              position: 'absolute',
              top: '100%',
              left: 0,
              right: 0,
              zIndex: 1000,
              maxHeight: '300px',
              overflowY: 'auto',
              backgroundColor: 'white',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            }}
            onScroll={handleScroll}
          >
            {data?.map((item, idx) => (
              <button
                key={item.id || idx}
                type='button'
                className='list-group-item list-group-item-action'
                onClick={() => handleSelectOption(item)}
              >
                {item?.name}
              </button>
            ))}
            {isLoading && (
              <div className='text-center p-2'>
                <span className='spinner-border spinner-border-sm me-2' />
                Loading...
              </div>
            )}
            {!isLoading && !hasMore && data.length > 0 && (
              <div className='text-center p-2 text-muted border-top'>No more results</div>
            )}
          </div>
        )}
    </div>
  )
}

export default InfiniteSuggestionsSearch
