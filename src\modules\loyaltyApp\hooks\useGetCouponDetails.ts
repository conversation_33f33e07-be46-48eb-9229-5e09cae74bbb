import {useState} from 'react'
import useApi from '../../../services/useApi'
import {parseCouponDetails} from '../utils'

const useGetCouponDetails = () => {
  const Api = useApi()
  const [isLoading, setLoading] = useState(false)
  const [couponDetails, setCouponDetails] = useState<any[]>([])

  const getCouponDetails = async (customerEmail: string | null) => {
    const filters = {customer: customerEmail}
    return Api.get(`/customers/loyalty/customer/coupons`, {
      filters,
      setLoading: setLoading,
      onSuccess: ({data}) => {
        setCouponDetails(parseCouponDetails(data?.data?.coupons || []))
      },
    })
  }

  return {
    couponDetails,
    getCouponDetails,
    isOperationLoading: isLoading,
  }
}

export default useGetCouponDetails
