import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'

export default function useGetCustomerReward(customerId?: string) {
  const initialFilters = {
    page: 1,
    limit: 10,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters)
  const {data: response, isFetching} = Api.useGetQuery(
    customerId ? `/customers/${customerId}/rewards` : '',
    {
      queryId: customerId ? `customer-rewards-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId,
    }
  )
  return {
    rewards: response?.data || [],
    isLoading: isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
