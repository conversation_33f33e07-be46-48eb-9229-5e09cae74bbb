import React from 'react'
import {KTSVG} from '../../../_metronic/helpers'

const InfoField = ({icon, label, value, className = ''}: {icon?: string, label: string, value: string, className?: string}) => (
  <div className={`d-flex align-items-center mb-3 ${className}`}>
    {icon && (
      <div className='me-3'>
        <KTSVG path={icon} className='svg-icon-5 text-muted' />
      </div>
    )}
    <div className='flex-grow-1'>
      <span className='text-gray-700 fs-6'>{label}:</span>
      <span className='text-gray-800 fw-semibold ms-2'>{value}</span>
    </div>
  </div>
)

const AccountInfoSection = () => (
  <div className='row g-5 mb-5'>
    {/* General Information */}
    <div className='col-12'>
      <div className='card border-0 bg-light'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-5'>
            <KTSVG path='/media/icons/duotune/general/gen019.svg' className='svg-icon-2 text-primary me-2' />
            <h4 className='fw-bold m-0 text-gray-800'>General Information</h4>
          </div>
          <div className='row'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='Account Name'
                value='Acme Corporation'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Status'
                value='Active'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen018.svg'
                label='Rating'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen032.svg'
                label='Federal Tax ID'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen025.svg'
                label='Account Number'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen024.svg'
                label='Tier'
                value='Gold'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen016.svg'
                label='Industry'
                value='Technology'
              />
              <InfoField
                icon='/media/icons/duotune/communication/com011.svg'
                label='How did you hear about us?'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen005.svg'
                label='Account Type'
                value='Business'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Customer Priority'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/communication/com013.svg'
                label='Website'
                value='https://acme.com'
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Owner & Manager */}
    <div className='col-12'>
      <div className='card border-0 bg-light'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-5'>
            <KTSVG path='/media/icons/duotune/communication/com014.svg' className='svg-icon-2 text-primary me-2' />
            <h4 className='fw-bold m-0 text-gray-800'>Owner & Manager</h4>
          </div>
          <div className='row'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='Account Owner'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/communication/com011.svg'
                label='Account Manager Email'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Account Manager Assigned Date/Time'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/communication/com011.svg'
                label='Account Owner Email'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/communication/com005.svg'
                label='Account Manager Mobile'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='Account Manager'
                value='Sarah Johnson'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen016.svg'
                label='Account Manager Division'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* BC Information */}
    <div className='col-12'>
      <div className='card border-0 bg-light'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-5'>
            <KTSVG path='/media/icons/duotune/general/gen016.svg' className='svg-icon-2 text-primary me-2' />
            <h4 className='fw-bold m-0 text-gray-800'>BC Information</h4>
          </div>
          <div className='row'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen005.svg'
                label='BC Customer Group'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/finance/fin010.svg'
                label='BC Store Credit'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/communication/com013.svg'
                label='Cell/Mobile Number'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen025.svg'
                label='BC Group Name'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen032.svg'
                label='BC CustomerId'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/communication/com013.svg'
                label='SMS Consent'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/communication/com011.svg'
                label='BC Email'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='BC Modified Time'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Form Fields */}
    <div className='col-12'>
      <div className='card border-0 bg-light'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-5'>
            <KTSVG path='/media/icons/duotune/general/gen043.svg' className='svg-icon-2 text-primary me-2' />
            <h4 className='fw-bold m-0 text-gray-800'>Form Fields</h4>
          </div>
          <div className='row'>
            <div className='col-md-6'>
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Read & Agree Midwest Terms'
                value='Not Set'
              />
            </div>
            <div className='col-md-6'>
              <InfoField
                icon='/media/icons/duotune/general/gen005.svg'
                label='Are you a Business'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Custom & Other Fields */}
    <div className='col-12'>
      <div className='card border-0 bg-light'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-5'>
            <KTSVG path='/media/icons/duotune/general/gen032.svg' className='svg-icon-2 text-primary me-2' />
            <h4 className='fw-bold m-0 text-gray-800'>Custom & Other Fields</h4>
          </div>
          <div className='row'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Abuser'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Is Customer Portal'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Is Partner'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Is Prospect'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Is Vendor'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Is Deleted'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export default AccountInfoSection
