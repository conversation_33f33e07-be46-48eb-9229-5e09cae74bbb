import React from 'react'
import {KTSVG} from '../../../_metronic/helpers'

const InfoField = ({icon, label, value}: {icon?: string, label: string, value: string}) => (
  <div className='d-flex align-items-center mb-3'>
    {icon && (
      <div className='me-3'>
        <KTSVG path={icon} className='svg-icon-2 text-muted' />
      </div>
    )}
    <div className='flex-grow-1'>
      <div className='text-muted fs-7 fw-semibold'>{label}:</div>
      <div className='fs-6 fw-bold text-gray-800'>{value}</div>
    </div>
  </div>
)

const ActivityItem = ({icon, iconColor, title, subtitle, time}: {
  icon: string,
  iconColor: string,
  title: string,
  subtitle: string,
  time: string
}) => (
  <div className='d-flex align-items-start mb-4'>
    <div className={`symbol symbol-35px bg-light-${iconColor} me-4`}>
      <div className='symbol-label'>
        <KTSVG path={icon} className={`svg-icon-2 text-${iconColor}`} />
      </div>
    </div>
    <div className='flex-grow-1'>
      <div className='fw-bold text-gray-800 mb-1'>{title}</div>
      <div className='text-muted fs-7'>{subtitle}</div>
    </div>
    <div className='text-muted fs-7'>{time}</div>
  </div>
)

const SystemActivitySection = () => (
  <div className='row g-5 mb-5'>
    {/* System Information */}
    <div className='col-12'>
      <div className='card card-flush border'>
        <div className='card-header border-0 pt-6'>
          <div className='card-title'>
            <div className='d-flex align-items-center'>
              <KTSVG path='/media/icons/duotune/general/gen014.svg' className='svg-icon-2 text-primary me-2' />
              <h3 className='fw-bold m-0'>System Information</h3>
            </div>
          </div>
        </div>
        <div className='card-body pt-0'>
          <div className='row'>
            <div className='col-md-6'>
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Attempt1 date capture'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='Created By'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='User Who last modified Notes'
                value='Not Set'
              />
            </div>
            <div className='col-md-6'>
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='Last Modified By'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Notes Last Modified Date/Time'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Activity & Notes */}
    <div className='col-12'>
      <div className='card card-flush border'>
        <div className='card-header border-0 pt-6'>
          <div className='card-title'>
            <div className='d-flex align-items-center'>
              <KTSVG path='/media/icons/duotune/general/gen007.svg' className='svg-icon-2 text-primary me-2' />
              <h3 className='fw-bold m-0'>Activity & Notes</h3>
            </div>
          </div>
        </div>
        <div className='card-body pt-0'>
          {/* Activity Timeline */}
          <div className='mb-8'>
            <h4 className='fw-bold mb-4'>Activity Timeline</h4>
            <div className='timeline'>
              <ActivityItem
                icon='/media/icons/duotune/ecommerce/ecm002.svg'
                iconColor='success'
                title='Order #ORD-003 placed for $2,850'
                subtitle='by System'
                time='10/01/2024 at 16:00:00'
              />
              <ActivityItem
                icon='/media/icons/duotune/communication/com005.svg'
                iconColor='warning'
                title='Phone call regarding upcoming order'
                subtitle='by Sarah Johnson'
                time='08/01/2024 at 19:45:00'
              />
            </div>
          </div>

          {/* Customer Notes */}
          <div>
            <h4 className='fw-bold mb-4'>Customer Notes</h4>
            <div className='bg-light-primary rounded p-4'>
              <div className='d-flex align-items-start'>
                <div className='symbol symbol-35px bg-primary me-4'>
                  <div className='symbol-label'>
                    <span className='text-white fw-bold fs-6'>SJ</span>
                  </div>
                </div>
                <div className='flex-grow-1'>
                  <div className='d-flex align-items-center justify-content-between mb-2'>
                    <div className='fw-bold text-gray-800'>Sarah Johnson</div>
                    <div className='text-muted fs-7'>05/01/2024, 14:30:00</div>
                  </div>
                  <div className='text-gray-700'>Customer interested in bulk pricing for Q2 orders</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export default SystemActivitySection
