import React from 'react'

const SystemActivitySection = () => (
  <div className='row g-5 mb-5'>
    <div className='col-12'>
      <div className='card p-4 mb-4'>
        <div className='fw-bold mb-2'>System Information</div>
        <div>Attempt1 date capture: Not Set</div>
        <div>Created By: Not Set</div>
        <div>Last Modified By: Not Set</div>
        <div>User Who last modified Notes: Not Set</div>
        <div>Notes Last Modified Date/Time: Not Set</div>
      </div>
    </div>
    <div className='col-12'>
      <div className='card p-4 mb-4'>
        <div className='fw-bold mb-2'>Activity & Notes</div>
        <div className='mb-3'>
          <div className='fw-semibold mb-2'>Activity Timeline</div>
          <div className='border rounded p-3 mb-2 bg-light'>
            <div className='d-flex align-items-center mb-1'>
              <span className='me-2' style={{fontSize: 20}}>
                &#128230;
              </span>
              <span>Order #ORD-003 placed for $2,850</span>
              <span className='ms-auto text-muted' style={{fontSize: 12}}>
                10/01/2024 at 16:00:00
              </span>
            </div>
            <div className='d-flex align-items-center'>
              <span className='me-2' style={{fontSize: 20}}>
                &#128222;
              </span>
              <span>Phone call regarding upcoming order</span>
              <span className='ms-auto text-muted' style={{fontSize: 12}}>
                08/01/2024 at 19:45:00
              </span>
            </div>
          </div>
        </div>
        <div className='fw-semibold mb-1'>Customer Notes</div>
        <div>Sarah Johnson</div>
        <div className='text-muted'>Customer interested in bulk pricing for Q2 orders</div>
      </div>
    </div>
  </div>
)

export default SystemActivitySection
